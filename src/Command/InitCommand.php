<?php

namespace Flexiwind\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Yaml\Yaml;

class InitCommand extends Command
{
    protected static $defaultName = 'init';

    protected function configure()
    {
        $this
            ->setDescription('Initialise Flexiwind dans le projet courant');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $configFile = getcwd() . '/flexiwind.yaml';
        $cacheDir = getcwd() . '/.flexiwind';

        if (file_exists($configFile)) {
            $io->warning('Un fichier flexiwind.yaml existe déjà.');
            return Command::FAILURE;
        }

        $config = [
            'sources' => [
                [
                    'name' => '@flexiwind',
                    'url' => 'https://registry.flexiwind.dev/{name}.json',
                ],
            ],
            'cache' => '.flexiwind',
        ];

        file_put_contents($configFile, Yaml::dump($config, 4, 2));

        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0777, true);
        }

        $io->success('Flexiwind initialisé avec succès !');
        $io->text("→ Configuration : flexiwind.yaml\n→ Cache : .flexiwind/");

        return Command::SUCCESS;
    }
}
